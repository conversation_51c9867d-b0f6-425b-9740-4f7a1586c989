# from django.db.models.signals import post_save
# from django.dispatch import receiver
# from cls_backend.models import User
# from organization.constants import MEMBER
# from organization.models import Organization, OrganizationMember
# from organization.constants import *


# @receiver(post_save, sender=User)
# def assign_user_to_default_organization(sender, instance, created, **kwargs):
#     """
#     Tự động add user mới vào default organization nếu user chưa thuộc organization nào
#     """
#     if created:  # Chỉ xử lý khi user mới được tạo
#         # Kiểm tra user có thuộc organization nào chưa
#         is_member = OrganizationMember.objects.filter(user=instance).exists()
        
#         if not is_member:
#             # Tìm default organization
#             default_org = Organization.objects.filter(is_default=True).first()
            
#             if not default_org:
#                 # Nếu không có default organization, tạo mới
#                 default_org = Organization.objects.create(
#                     name="Default Organization",
#                     description="Tổ chức mặc định cho các tài khoản mới",
#                     status=ACTIVE,
#                     is_default=True
#                 )
            
#             # Tạo OrganizationMember
#             OrganizationMember.objects.create(
#                 user=instance,
#                 organization=default_org,
#                 role=MEMBER,
#                 is_active=True
#             )
