import requests
from decouple import config


def send_discord_message(message_content: str):
    DISCORD_WEBHOOK = config("DISCORD_WEBHOOK")
    payload = {
        "content": message_content
    }

    response = requests.post(DISCORD_WEBHOOK, json=payload)

    if response.status_code == 204:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message. Status code: {response.status_code}")
        print(response.text)

if __name__=='__main__':
    send_discord_message('Hello world')