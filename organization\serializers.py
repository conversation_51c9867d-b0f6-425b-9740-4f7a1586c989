from rest_framework import serializers

from organization.organization_member.serializers import OrganizationMemberSerializer
from .models import Organization, OrganizationMember


class OrganizationSerializer(serializers.ModelSerializer):
    count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Organization
        fields = ['id', 'name', 'description', 'parent_organization', 'created_at', 'updated_at', 'represent_people', 'address', 'phone', 'email', 'status', 'count']
        read_only_fields = ['id', 'status', 'created_at', 'updated_at']

    def get_count(self, obj):
        count = OrganizationMember.objects.filter(organization_id=obj.id).count()

        
        
        
        
        return count

class OrganizationDetailSerializer(serializers.ModelSerializer):
    members = OrganizationMemberSerializer(many=True, read_only=True)
    member_count = serializers.SerializerMethodField()

    class Meta:
        model = Organization
        fields = ['id', 'name', 'description', 'members', 'member_count', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_member_count(self, obj):
        return obj.members.filter(is_active=True).count()