from rest_framework import serializers

from cls_backend.models import Position, User
from cls_backend.utils.file_utils import generate_presigned_url
from organization.models import Organization, OrganizationMember
from organization.constants import ADMIN, MEMBER

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    # Thêm các trường cho Organization Member
    organization = serializers.UUIDField(required=False, write_only=True)
    role = serializers.CharField(required=False, write_only=True)

    class Meta:
        model = User
        fields = ['id', 'email', 'password', 'avatar', 'fullname', 'is_staff', 'phone', 'position', 'gender', 'dob', 'address', 'organization', 'role']
        read_only_fields = ['email', 'password']


    def validate_role(self, value):
        """Validate role field"""
        if value and value not in [ADMIN, MEMBER]:
            raise serializers.ValidationError(f"Role must be either '{ADMIN}' or '{MEMBER}'")
        return value

    def get_queryset(self):
        queryset = Position.objects.all()
        organization_id = self.request.query_params.get('organization')
        if organization_id:
            queryset = queryset.filter(organization_id=organization_id)
        return queryset
    
    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

    def update(self, instance, validated_data):
        # Không cho update email và password qua API này
        validated_data.pop('email', None)
        validated_data.pop('password', None)

        # Xử lý cập nhật Organization Member
        organization_id = validated_data.pop('organization', None)
        role = validated_data.pop('role', None)

        # Cập nhật thông tin User trước
        user = super().update(instance, validated_data)

        # Nếu có thông tin organization và role, cập nhật OrganizationMember
        if organization_id and role:
            try:
                organization = Organization.objects.get(id=organization_id)

                # Tìm hoặc tạo OrganizationMember
                org_member, created = OrganizationMember.objects.get_or_create(
                    user=user,
                    organization=organization,
                    defaults={'role': role, 'is_active': True}
                )

                # Nếu đã tồn tại, cập nhật role
                if not created:
                    org_member.role = role
                    org_member.save()

            except Organization.DoesNotExist:
                raise serializers.ValidationError({
                    "organization": f"Organization with id {organization_id} does not exist"
                })

        return user

    def to_representation(self, instance):
        data = super().to_representation(instance)

        
        if instance.is_superuser:
            data['system_role'] = 'SUPER_ADMIN'
        elif instance.is_staff:
            data['system_role'] = 'ADMIN'
        else:
            data['system_role'] = 'USER'
        data.pop('is_staff')

        # Tạo presigned URL cho avatar nếu có
        if instance.avatar:
            avatar_url = generate_presigned_url(instance.avatar.name, expiration=3600)
            data['avatar'] = avatar_url
        else:
            data['avatar'] = None

        # Trả về toàn bộ thông tin của position (id, title)
        if instance.position:
            data['position'] = {
                'id': instance.position.id,
                'title': instance.position.title
            }
        else:
            data['position'] = None

        # Thêm thông tin organization memberships
        org_memberships = OrganizationMember.objects.filter(user=instance).select_related('organization')
        data['organization_memberships'] = [
            {
                'organization_id': str(membership.organization.id),
                'organization_name': membership.organization.name,
                'role': membership.role,
                'is_active': membership.is_active,
                'joined_at': membership.joined_at
            }
            for membership in org_memberships
        ]

        # Thêm trạng thái is_active tổng hợp (true nếu có ít nhất 1 membership active)
        data['active'] = any(membership.is_active for membership in org_memberships)

        # Nếu có filter theo organization_id trong request, trả về is_active của organization đó
        request = self.context.get('request')
        if request and hasattr(request, 'query_params'):
            organization_id = request.query_params.get('organization_id')
            if organization_id:
                specific_membership = org_memberships.filter(organization_id=organization_id).first()
                data['current_organization_is_active'] = specific_membership.is_active if specific_membership else False

        return data

