from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from cls_backend.models import Position, User
from cls_backend.modules.position.permissions import IsSuperAdminOrOrganizationAdmin
from organization.models import OrganizationMember
from organization.constants import *
from .serializers import PositionSerializer

class PositionViewSet(viewsets.ModelViewSet):
    permission_classes = [IsSuperAdminOrOrganizationAdmin]
    queryset = Position.objects.all()
    serializer_class = PositionSerializer
    
    def perform_create(self, serializer):
        user = self.request.user
        organization = serializer.validated_data.get("organization")

        if user.is_superuser:
            serializer.save()
            return

        is_admin = OrganizationMember.objects.filter(
            user=user,
            organization=organization,
            role=ADMIN,
            is_active=True
        ).exists()

        if not is_admin:
            raise PermissionDenied("Bạn không có quyền tạo position trong tổ chức này.")

        serializer.save()
        
    def get_queryset(self):        
        organization_id = self.request.query_params.get('organization', None)
        search = self.request.query_params.get('search', None)
        status = self.request.query_params.get('status', None)
        
        queryset = self.queryset
        if organization_id:
            queryset = queryset.filter(organization_id=organization_id)
        if search:
            queryset = queryset.filter(title__icontains=search)
        if status:
            queryset = queryset.filter(status=status)
            
        user = self.request.user
        
        if user.is_superuser:
            return queryset

        # Lọc theo organization user là admin nếu không phải is_superuser
        admin_orgs = OrganizationMember.objects.filter(
            user=user,
            role=ADMIN,
            is_active=True
        ).values_list('organization_id', flat=True)

        if organization_id:
            if str(organization_id) not in map(str, admin_orgs):
                return queryset.none()
            return queryset

        # Nếu không truyền organization, trả về tất cả position thuộc các org user là admin
        if not admin_orgs:
            return queryset.none()
        
        return queryset.filter(organization_id__in=admin_orgs)
    
    @action(detail=True, methods=['POST'], url_path='set-status')
    def set_position_status(self, request, pk):
        req_status = request.data.get('status', None)
        valid_statuses = [choice[0] for choice in Position.STATUS_CHOICES]
        if req_status not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of {valid_statuses}."},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        position = self.get_object()
        position.status=req_status
        position.save()
        
        users = 0
        if not position.status:  
            users = User.objects.filter(position=position).update(position=None)
        
        return Response({
            "detail": "Cập nhật trạng thái position thành công.",
            "users_updated": users
        }, status=status.HTTP_200_OK)
