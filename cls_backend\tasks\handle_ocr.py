import json
import os
from pathlib import Path
import requests
from celery import shared_task
from decouple import config
from django.core.files.base import ContentFile
from django.db import transaction, DatabaseError
from django.db.transaction import TransactionManagementError
from cls_backend.constants import TASK_TIMEOUT, OCR_TIMEOUT
from django.db.models import Count
from django.core.serializers.json import DjangoJSONEncoder
from cls_backend import constants as const
from cls_backend.models import Document
from cls_backend.modules.notification.event import EVENT_CDVB_DONE
from cls_backend.modules.notification.utils import send_notifications
from cls_backend.modules.ocr.serializers import DocumentSerializer
from cls_backend.utils.cal_timeout_acord_page import get_timeout_limit_from_document_id
from cls_backend.utils.file_utils import generate_presigned_url
from cls_backend.utils.timeout_terminate_celery import BaseTask
from .handle_ie import handle_ie
from cls_backend.tasks.handle_extract_json import handle_extract_json
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from django.core.files.base import ContentFile
from cls_backend.utils.handle_file import RedisUtil
import logging
from contextlib import contextmanager
import time
from .handle_conflict import handle_conflict
import subprocess
import tempfile

logger = logging.getLogger("cls")

def safe_handle_extract_json(document):
    """
    Wrapper function để gọi handle_extract_json một cách an toàn
    """
    try:
        logger.debug(f"Starting JSON extraction for document {document.id}")
        handle_extract_json(document=document)
        logger.debug(f"Successfully completed JSON extraction for document {document.id}")
    except Exception as e:
        logger.exception(f"Error in handle_extract_json for document {document.id}: {e}")
        raise


def wait_for_file_ready(file_field, max_wait_time=30, wait_interval=1):
    """
    Đợi cho đến khi file sẵn sàng để đọc
    Returns True nếu file sẵn sàng, False nếu timeout
    """
    waited_time = 0
    while waited_time < max_wait_time:
        try:
            # Kiểm tra file có tồn tại và có thể đọc được không
            if (file_field and
                file_field.name and
                file_field.storage.exists(file_field.name)):
                # Thử đọc một phần nhỏ của file để đảm bảo nó hoàn chỉnh
                with file_field.open('rb') as test_file:
                    test_file.read(1024)  # Đọc 1KB đầu tiên
                logger.debug(f"File đã sẵn sàng: {file_field.name}")
                return True
        except Exception as e:
            logger.debug(f"File chưa sẵn sàng, đợi thêm... ({waited_time}s): {str(e)}")
            time.sleep(wait_interval)
            waited_time += wait_interval

    logger.error(f"Timeout waiting for file to be ready after {max_wait_time}s")
    return False


def create_requests_session():
    """Create a requests session with retry strategy"""
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[408, 429, 500, 502, 503, 504]
    )
    session.mount('http://', HTTPAdapter(max_retries=retries))
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

@contextmanager
def timeout_transaction(timeout_seconds=TASK_TIMEOUT):  # 5 minutes default timeout
    """
    Context manager để xử lý transaction với timeout.
    Tự động rollback khi có exception hoặc timeout.
    """
    start_time = time.time()
    try:
        with transaction.atomic():
            yield
            # Kiểm tra timeout sau khi hoàn thành
            if time.time() - start_time > timeout_seconds:
                raise TimeoutError("Transaction timeout exceeded")
    except Exception as e:
        logger.exception(f"Transaction failed: {e}")
        # Django tự động rollback transaction khi có exception trong atomic block
        raise

def convert_doc_to_docx(input_path, output_dir):
    """
    Convert a .doc file to .docx using LibreOffice.
    Returns the path to the converted .docx file.
    """
    try:
        # soffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
        subprocess.run([
            # soffice_path, 
            "soffice",
            "--headless", "--convert-to", "docx", "--outdir", output_dir, input_path
        ], check=True)
        base = os.path.splitext(os.path.basename(input_path))[0]
        return os.path.join(output_dir, base + ".docx")
    except subprocess.CalledProcessError as e:
        logger.error(f"LibreOffice conversion failed: {e}")
        return None

def is_already_converted(document):
    return document.is_convert and document.convert

def is_doc_file(document):
    return document.name.lower().endswith('.doc')

def is_docx_file(document):
    return document.name.lower().endswith('.docx')

def mark_failed(document, reason=None):
    logger.error(reason or "Marking document as FAILED")
    document.status = const.STATUS_FAILED
    document.save(update_fields=['status'])

def mark_success(document):
    document.status = const.STATUS_SUCCESS
    document.save(update_fields=['status'])

def run_post_process(document, soft_timeout, hard_timeout):
    handle_conflict.delay(document_id=document.id)
    handle_ie.apply_async(
        kwargs={'document_id': document.id},
        queue='ocr',
        soft_time_limit=soft_timeout,
        time_limit=hard_timeout
    )

def convert_doc_to_docx_and_update(document):
    with tempfile.TemporaryDirectory() as tmp_dir:
        local_doc_path = os.path.join(tmp_dir, os.path.basename(document.origin.name))

        with document.origin.open('rb') as f, open(local_doc_path, 'wb') as out_f:
            out_f.write(f.read())

        docx_path = convert_doc_to_docx(local_doc_path, tmp_dir)
        if not docx_path or not os.path.exists(docx_path):
            return False

        logger.debug('Upload docx to s3')
        with open(docx_path, 'rb') as docx_file:
            document.origin.save(os.path.basename(docx_path), ContentFile(docx_file.read()), save=True)
        return True

def validate_document_file(document):
    if not document.origin or not document.origin.storage.exists(document.origin.name):
        logger.warning(f"Document {document.id} does not have a valid origin file. Skipping OCR.")
        return False
    return True

def run_ocr_pipeline(self, document, soft_timeout):
    s3_path = f"s3://{config('AWS_STORAGE_BUCKET_NAME')}/{document.origin.name}"
    payload = {
        'return_type': 'word',
        'langs': 'vn',
        'is_tile': 0,
        'is_full_line': 1,
        'is_extract_figure': 0,
        's3_file': s3_path
    }

    upload_url = config('OCR_HOST') + config('EXTRACT_OCR')
    get_data_url = config('OCR_HOST') + config('OCR_CHECK_STATUS')
    session = create_requests_session()

    response = session.post(upload_url, data=payload, timeout=(1, 10), stream=True)
    logger.debug(response.text)

    if response.status_code != 200 or response.json().get('status') != "Success":
        logger.error(f"OCR API returned failure: {response.status_code}")
        mark_failed(document)
        return False

    job_id = response.json()['session_id']
    if not RedisUtil.check_job_status_with_timeout(job_id, 'ocr', soft_timeout, check_interval=3):
        mark_failed(document, reason="OCR job timeout")
        return False

    result_data = get_ocr_result(get_data_url, job_id)
    if not result_data:
        mark_failed(document, reason="Failed to get OCR result")
        return False

    return save_ocr_result_to_document(document, result_data)

def get_ocr_result(url, job_id):
    try:
        response = requests.post(url, data={'session_id': job_id, 'return_type': 'word'})
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logger.error(f"Error while getting OCR result: {e}")
    return None

def save_ocr_result_to_document(document, result_data):
    if result_data.get('status') != "Success":
        logger.error(f"OCR result status not success: {result_data.get('status')}")
        return False

    base, _ = os.path.splitext(document.origin.name)
    docx_path = f"{base}.docx"
    docx_url = generate_presigned_url(docx_path)
    if not docx_url:
        logger.error(f"Could not generate URL for {docx_path}")
        return False

    s3_response = requests.get(docx_url)
    if s3_response.status_code != 200 or len(s3_response.content) == 0:
        logger.error(f"Could not download file from {docx_url}")
        return False

    document.convert.save(docx_path, ContentFile(s3_response.content), save=True)

    if not wait_for_file_ready(document.convert):
        return False

    document.is_convert = True
    document.status = const.STATUS_SUCCESS
    document.save(update_fields=['is_convert', 'status'])
    return True

def send_document_notification(document, user_id):
    document.percentage = 25
    document.save(update_fields=['percentage'])
    data = json.loads(json.dumps(DocumentSerializer(document).data, cls=DjangoJSONEncoder))
    send_notifications(user_id=user_id, event=EVENT_CDVB_DONE, data=data, percentage=25)

@shared_task(bind=True, max_retries=2)
def handle_ocr(self, document_id, skip_notif=False, skip_process=False):
    try:
        logger.debug(f"Start handling OCR document id {document_id}")
        document = Document.objects.get(pk=document_id)
        user_id = document.created_by.id
        soft_timeout, hard_timeout = get_timeout_limit_from_document_id(document.id)

        if is_already_converted(document) and not skip_process:
            if not wait_for_file_ready(document.convert):
                mark_failed(document, reason="File convert not ready")
                return
            logger.debug("Document is converted, run post-processing tasks")
            run_post_process(document, soft_timeout, hard_timeout)
            return

        # Chuyển file .doc sang docx nếu cần
        if is_doc_file(document):
            if not convert_doc_to_docx_and_update(document):
                mark_failed(document, reason="Failed to convert DOC to DOCX")
                return
            # Sau khi convert .doc thành .docx, xử lý như file docx
            logger.debug("DOC converted to DOCX, handle via local OCR extract")
            document.status = const.STATUS_OCR
            document.save(update_fields=['status'])

            try:
                safe_handle_extract_json(document)
            except Exception as e:
                mark_failed(document, reason=f"Extract JSON failed: {str(e)}")
                return
            if skip_process:
                mark_success(document)
                return

            if not wait_for_file_ready(document.origin):
                mark_failed(document, reason="File origin not ready")
                return

            logger.debug(f"Call handle_conflict for document {document.id}")
            run_post_process(document, soft_timeout, hard_timeout)
            return

        # Nếu là file docx => xử lý trực tiếp
        if is_docx_file(document):
            logger.debug("Handle DOCX via local OCR extract")
            document.status = const.STATUS_OCR
            document.save(update_fields=['status'])

            try:
                safe_handle_extract_json(document)
            except Exception as e:
                mark_failed(document, reason=f"Extract JSON failed: {str(e)}")
                return
            if skip_process:
                mark_success(document)
                return

            if not wait_for_file_ready(document.origin):
                mark_failed(document, reason="File origin not ready")
                return

            logger.debug(f"Call handle_conflict for document {document.id}")
            run_post_process(document, soft_timeout, hard_timeout)
            return

        # Bắt đầu OCR online
        document.status = const.STATUS_OCR
        document.save(update_fields=['status'])

        if not validate_document_file(document):
            mark_success(document)
            return

        if not run_ocr_pipeline(self, document, soft_timeout):
            return

        try:
            safe_handle_extract_json(document)
        except Exception as e:
            mark_failed(document, reason=f"Extract JSON failed: {str(e)}")
            return

        if skip_process:
            mark_success(document)
            return

        run_post_process(document, soft_timeout, hard_timeout)

    except Exception as e:
        logger.exception(e)
        mark_failed(document)
        raise e

    if not skip_notif:
        send_document_notification(document, user_id)
