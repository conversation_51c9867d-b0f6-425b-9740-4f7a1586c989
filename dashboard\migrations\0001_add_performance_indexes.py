# Generated by Django for performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):
    atomic = False  # Required for CREATE INDEX CONCURRENTLY

    dependencies = [
        ('cls_backend', '0001_initial'),  # Adjust based on your actual dependencies
        ('chatbot', '0001_initial'),
        ('request_log', '0001_initial'),
        ('organization', '0007_auto_20250805_1203'),
    ]

    operations = [
        # Add indexes for User model
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_created_time ON cls_backend_user(created_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_created_time;"
        ),
        
        # Add indexes for ChatMessage model
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chatmessage_created_at_type ON chatbot_chatmessage(created_at, type);",
            reverse_sql="DROP INDEX IF EXISTS idx_chatmessage_created_at_type;"
        ),
        
        # Add indexes for RequestLog model
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_requestlog_timestamp_feature ON request_log_requestlog(timestamp, feature_name);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_timestamp_feature;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_requestlog_user_timestamp ON request_log_requestlog(user_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_user_timestamp;"
        ),
        
        # Add indexes for Conversation model
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conversation_created_at ON chatbot_conversation(created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_conversation_created_at;"
        ),
        
        # Add composite indexes for organization queries
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_member_org_id ON organization_organizationmember(organization_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_org_member_org_id;"
        ),
    ]
