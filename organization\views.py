# Create your views here.
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404

from .models import Organization, OrganizationMember
from .serializers import OrganizationSerializer
from .permissions import IsOrganizationAdminOrSuperAdmin
from .constants import *

from cls_backend.models import User
from organization.organization_member.serializers import OrganizationMemberSerializer

class OrganizationViewSet(viewsets.ModelViewSet):
    queryset = Organization.objects.all().order_by('-created_at')
    permission_classes = [IsOrganizationAdminOrSuperAdmin]
    serializer_class = OrganizationSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        status_filter = self.request.query_params.get('status')

        if search:
            queryset = queryset.filter(name__icontains=search)

        if status_filter:
            # Validate status value
            valid_statuses = [choice[0] for choice in Organization.STATUS_CHOICES]
            if status_filter in valid_statuses:
                queryset = queryset.filter(status=status_filter)

        return queryset
    
    def update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return super().update(request, *args, **kwargs)
    
    @action(detail=True, methods=['POST'], url_path='set-status')
    def set_organization_status(self, request, pk):
        req_status = request.data.get('status', None)
        valid_statuses = [choice[0] for choice in Organization.STATUS_CHOICES]
        if req_status not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Must be one of {valid_statuses}."},
                status=status.HTTP_400_BAD_REQUEST
            )

        affected_orgs = []
        def update_status_recursive(org_instance):
            org_instance.status = req_status
            org_instance.save(update_fields=["status"])
            affected_orgs.append(org_instance)
            for child in org_instance.departments.all():
                update_status_recursive(child)

        root_org = Organization.objects.get(id=pk)
        update_status_recursive(root_org)
        
        is_active_value = req_status == ACTIVE
        user_ids = OrganizationMember.objects.filter(
            organization__in=affected_orgs
        ).values_list('user_id', flat=True).distinct()
        
        User.objects.filter(id__in=user_ids).update(is_active=is_active_value)
        
        sub_org_count = len(affected_orgs) - 1 if len(affected_orgs) >= 1 else 0
        return Response(
            {"message": f"Status updated to {req_status} for {root_org.name} and its {sub_org_count} sub-organizations."},
            status=status.HTTP_200_OK
        )
        
            
    @action(detail=True, methods=['POST'], url_path='massive-add-users')
    def massive_add_users(self, request, pk=None):
        """
        Add multiple users to an organization by username pattern.
        Expects JSON: { "username_pattern": "prefix*" }
        """
        username_pattern = request.data.get('username_pattern')
        role = request.data.get('role', 'member')
        if not username_pattern:
            return Response({'detail': 'username_pattern is required.'}, status=status.HTTP_400_BAD_REQUEST)
        organization = get_object_or_404(Organization, pk=pk)

        # Convert pattern to Django __startswith or __icontains
        if username_pattern.endswith('*'):
            pattern = username_pattern[:-1]
            users = User.objects.filter(email__startswith=pattern)
        elif '*' in username_pattern:
            pattern = username_pattern.replace('*', '')
            users = User.objects.filter(email__icontains=pattern)
        else:
            users = User.objects.filter(email=username_pattern)

        if not users.exists():
            return Response({'detail': 'No users found matching the pattern.'}, status=status.HTTP_404_NOT_FOUND)

        added = []
        skipped = []
        for user in users:
            # Check if already a member
            if OrganizationMember.objects.filter(organization=organization, user=user).exists():
                skipped.append(user.email)
                continue
            OrganizationMember.objects.create(
                organization=organization,
                user=user,
                role=role
            )
            added.append(user.email)

        return Response({
            'total_added': len(added),
            'total_skipped': len(skipped)
        }, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=['GET'], url_path='users')
    def users(self, request, pk=None):
        members = OrganizationMember.objects.filter(organization_id=pk).select_related('user')
        queryset = self.paginate_queryset(members)
        serializer = OrganizationMemberSerializer(queryset, many=True)
        return self.get_paginated_response(serializer.data)
    
    def destroy(self, request, *args, **kwargs):
        return Response({'message': 'Organization cant be deleted'}, status=status.HTTP_403_FORBIDDEN)