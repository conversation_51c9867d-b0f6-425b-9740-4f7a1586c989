from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils.dateparse import parse_datetime
from django.db.models import Count, Q
from django.core.cache import cache
from chatbot.constants import USER_MESSAGE
from chatbot.models import ChatMessage, Conversation
from dashboard.constant import DECREASE, INCREASE, NO_CHANGE
from organization.models import Organization, OrganizationMember
from cls_backend.models import User, Document
from django.db import models
from request_log.models import RequestLog
from organization.constants import ADMIN
from datetime import datetime, timedelta
from cls_backend.utils.es_utils import init_es_client
from decouple import config
from cls_backend.constants import (
    QUYET_DINH, THONG_TU, NGHI_QUYET, NGHI_DINH, THONG_TU_LIEN_TICH,
    LUAT, VAN_BAN_HOP_NHAT, PHAP_LENH, CONG_VAN, BO_LUAT,
    NGHI_QUYET_LIEN_TICH, CHI_THI, VAN_BAN_KHAC, LENH, HIEN_PHAP,
    VAN_BAN_LIEN_QUAN, THONG_BAO, CHUONG_TRINH, SAC_LENH, THONG_TU_LIEN_BO,
    HIEP_DINH, SAC_LUAT, BAO_CAO, CONG_DIEN, DIEU_UOC_QUOC_TE,
    HUONG_DAN, KE_HOACH, VAN_BAN_WTO, DU_THAO
)

# Constants
ALLOWED_FEATURE_NAMES = ['legal_search', 'note_create', 'document_create', 'search_law_clauses', 'compare', 'create_workspace']

class DashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # Extract and validate parameters
        params = self._extract_parameters(request)
        
        # Check permissions and get base filters
        base_filters = self._get_base_filters(params['user'], params['organization_id'])
        if base_filters is None:
            return Response(
                {"detail": "You do not have permission to access this data."}, 
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate metrics using optimized queries
        metrics = self._calculate_metrics(params, base_filters)
        
        # Generate chart data
        chart_data = self._generate_chart_data(params, base_filters)
        
        # Combine response
        response_data = {**metrics, **chart_data}
        
        # Add organization data for superusers
        if params['user'].is_superuser and not params['organization_id']:
            response_data['organizations_data'] = self._get_organizations_data()
        
        return Response(response_data)

    def _extract_parameters(self, request):
        """Extract and parse request parameters"""
        from_date_str = request.query_params.get('from_date')
        to_date_str = request.query_params.get('to_date')
        
        return {
            'user': request.user,
            'organization_id': request.query_params.get('organization_id'),
            'from_date_dt': parse_datetime(from_date_str) if from_date_str else None,
            'to_date_dt': parse_datetime(to_date_str) if to_date_str else None,
            'config': request.query_params.get('config', 'day'),
        }

    def _get_base_filters(self, user, organization_id):
        """Get base queryset filters based on user permissions"""
        if user.is_superuser:
            if organization_id:
                return {'organization_filter': Q(organization_memberships__organization_id=organization_id)}
            return {'organization_filter': Q()}  # No filter for superuser without org_id
        
        # Non-superuser must have admin role in the organization
        if organization_id and user.organization_memberships.filter(
            organization_id=organization_id, role=ADMIN
        ).exists():
            return {'organization_filter': Q(organization_memberships__organization_id=organization_id)}
        
        return None  # Permission denied

    def _calculate_metrics(self, params, base_filters):
        """Calculate all dashboard metrics using optimized queries"""
        from_date_dt = params['from_date_dt']
        to_date_dt = params['to_date_dt']
        organization_id = params['organization_id']
        user = params['user']
        
        # Calculate date ranges for comparison
        prev_from_date_dt, prev_to_date_dt = None, None
        if from_date_dt and to_date_dt:
            period = to_date_dt - from_date_dt
            prev_from_date_dt = from_date_dt - period
            prev_to_date_dt = from_date_dt

        # Use single queries with conditional aggregation
        metrics = {}
        
        # Users metrics
        user_stats = self._get_user_stats(
            base_filters['organization_filter'], 
            from_date_dt, to_date_dt, 
            prev_from_date_dt, prev_to_date_dt,
            user.is_superuser and not organization_id
        )
        metrics.update(user_stats)
        
        # Requests metrics (ChatMessage + RequestLog)
        request_stats = self._get_request_stats(
            base_filters['organization_filter'],
            from_date_dt, to_date_dt,
            prev_from_date_dt, prev_to_date_dt,
            organization_id
        )
        metrics.update(request_stats)
        
        # Conversations metrics
        conversation_stats = self._get_conversation_stats(
            base_filters['organization_filter'],
            from_date_dt, to_date_dt,
            prev_from_date_dt, prev_to_date_dt,
            organization_id
        )
        metrics.update(conversation_stats)
        
        # Active users metrics
        active_user_stats = self._get_active_user_stats(
            base_filters['organization_filter'],
            from_date_dt, to_date_dt,
            prev_from_date_dt, prev_to_date_dt,
            organization_id
        )
        metrics.update(active_user_stats)
        
        return metrics

    def _get_user_stats(self, org_filter, from_date_dt, to_date_dt, prev_from_date_dt, prev_to_date_dt, include_total=True):
        """Get user statistics with single query"""
        base_query = User.objects.filter(org_filter)
        
        stats = {}
        if include_total:
            stats['total_users'] = base_query.count()
        
        # Current period new users
        if from_date_dt and to_date_dt:
            stats['new_users'] = base_query.filter(
                created_time__gte=from_date_dt, 
                created_time__lte=to_date_dt
            ).count()
        else:
            stats['new_users'] = 0
            
        # Previous period new users
        if prev_from_date_dt and prev_to_date_dt:
            stats['prev_new_users'] = base_query.filter(
                created_time__gte=prev_from_date_dt, 
                created_time__lt=prev_to_date_dt
            ).count()
        else:
            stats['prev_new_users'] = None
            
        # Calculate changes
        change_data = self._calculate_change(stats['new_users'], stats['prev_new_users'])
        stats.update({
            'percent_change': change_data['percent'],
            'change_type': change_data['type'],
            'change_amount': change_data['amount']
        })
        
        return stats

    def _get_request_stats(self, org_filter, from_date_dt, to_date_dt, prev_from_date_dt, prev_to_date_dt, organization_id):
        """Get request statistics (ChatMessage + RequestLog) with optimized queries"""
        # Current period
        chat_count = self._get_chat_message_count(org_filter, from_date_dt, to_date_dt, organization_id)
        request_count = self._get_request_log_count(org_filter, from_date_dt, to_date_dt, organization_id)
        total_requests = chat_count + request_count
        
        # Previous period
        prev_requests = None
        if prev_from_date_dt and prev_to_date_dt:
            prev_chat_count = self._get_chat_message_count(org_filter, prev_from_date_dt, prev_to_date_dt, organization_id)
            prev_request_count = self._get_request_log_count(org_filter, prev_from_date_dt, prev_to_date_dt, organization_id)
            prev_requests = prev_chat_count + prev_request_count
        
        # Calculate changes
        change_data = self._calculate_change(total_requests, prev_requests)
        
        return {
            'total_requests': total_requests,
            'prev_requests': prev_requests,
            'percent_change_requests': change_data['percent'],
            'change_type_requests': change_data['type'],
            'change_amount_requests': change_data['amount']
        }

    def _get_conversation_stats(self, org_filter, from_date_dt, to_date_dt, prev_from_date_dt, prev_to_date_dt, organization_id):
        """Get conversation statistics with optimized queries"""
        base_query = Conversation.objects.all()
        if organization_id:
            base_query = base_query.filter(workspace__user__organization_memberships__organization_id=organization_id)
        
        # Current period - Note: there seems to be a bug in original code using prev dates for current
        total_conversations = base_query.count()
        
        # Previous period
        prev_conversations = None
        if prev_from_date_dt and prev_to_date_dt:
            prev_conversations = base_query.filter(
                created_at__gte=prev_from_date_dt, 
                created_at__lt=prev_to_date_dt
            ).count()
        
        # Calculate changes
        change_data = self._calculate_change(total_conversations, prev_conversations)
        
        return {
            'total_conversations': total_conversations,
            'prev_conversations': prev_conversations,
            'percent_change_conversations': change_data['percent'],
            'change_type_conversations': change_data['type'],
            'change_amount_conversations': change_data['amount']
        }

    def _get_active_user_stats(self, org_filter, from_date_dt, to_date_dt, prev_from_date_dt, prev_to_date_dt, organization_id):
        """Get active user statistics"""
        base_query = RequestLog.objects.all()
        if organization_id:
            base_query = base_query.filter(user__organization_memberships__organization_id=organization_id)
        
        # Current period
        active_users = 0
        if from_date_dt and to_date_dt:
            active_users = base_query.filter(
                timestamp__gte=from_date_dt, 
                timestamp__lte=to_date_dt
            ).values('user_id').distinct().count()
        
        # Previous period
        prev_active_users = None
        if prev_from_date_dt and prev_to_date_dt:
            prev_active_users = base_query.filter(
                timestamp__gte=prev_from_date_dt, 
                timestamp__lt=prev_to_date_dt
            ).values('user_id').distinct().count()
        
        # Calculate changes
        change_data = self._calculate_change(active_users, prev_active_users)
        
        return {
            'active_users': active_users,
            'prev_active_users': prev_active_users,
            'change_type_active_users': change_data['type'],
            'change_amount_active_users': change_data['amount']
        }

    def _get_chat_message_count(self, org_filter, from_date_dt, to_date_dt, organization_id):
        """Get ChatMessage count with optimized query"""
        if not from_date_dt or not to_date_dt:
            # For total count without date filter
            query = ChatMessage.objects.filter(type=USER_MESSAGE)
            if organization_id:
                query = query.filter(conversation__workspace__user__organization_memberships__organization_id=organization_id)
            return query.count()
        
        query = ChatMessage.objects.filter(
            type=USER_MESSAGE,
            created_at__gte=from_date_dt, 
            created_at__lte=to_date_dt
        )
        if organization_id:
            query = query.filter(conversation__workspace__user__organization_memberships__organization_id=organization_id)
        return query.count()

    def _get_request_log_count(self, org_filter, from_date_dt, to_date_dt, organization_id):
        """Get RequestLog count with optimized query"""
        query = RequestLog.objects.filter(feature_name__in=ALLOWED_FEATURE_NAMES)
        
        if from_date_dt and to_date_dt:
            query = query.filter(timestamp__gte=from_date_dt, timestamp__lte=to_date_dt)
        
        if organization_id:
            query = query.filter(user__organization_memberships__organization_id=organization_id)
        
        return query.count()

    def _calculate_change(self, current, previous):
        """Calculate percentage and absolute change between periods"""
        if previous is None:
            return {'percent': None, 'type': None, 'amount': None}
        
        amount = current - previous
        
        if previous == 0:
            percent = 0.0
        else:
            percent = (amount / previous) * 100
        
        if amount > 0:
            change_type = INCREASE
        elif amount < 0:
            change_type = DECREASE
        else:
            change_type = NO_CHANGE
            
        return {
            'percent': percent,
            'type': change_type,
            'amount': amount
        }

    def _get_organizations_data(self):
        """Get organization data for superusers with single query"""
        cache_key = 'dashboard_organizations_data'
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data
            
        total_users = User.objects.count()
        organizations_data = []
        
        # Use select_related and annotations to optimize
        organizations = Organization.objects.annotate(
            user_count=Count('members')
        ).all()
        
        for org in organizations:
            org_percentage = (org.user_count / total_users * 100) if total_users > 0 else 0
            organizations_data.append({
                'organization_name': org.name,
                'user_count': org.user_count,
                'percentage': round(org_percentage, 2)
            })
        
        # Cache for 5 minutes
        cache.set(cache_key, organizations_data, 300)
        return organizations_data

    def _generate_chart_data(self, params, base_filters):
        """Generate chart data for all metrics"""
        from_date_dt = params['from_date_dt']
        to_date_dt = params['to_date_dt']
        config = params['config']
        organization_id = params['organization_id']
        
        if not from_date_dt or not to_date_dt:
            return {
                'chart_new_users': [],
                'chart_new_conversations': [],
                'chart_active_users': []
            }
        
        return {
            'chart_new_users': self._get_chart_data(
                from_date_dt, to_date_dt, config, organization_id, 'users'
            ),
            'chart_new_conversations': self._get_detailed_chart_data(
                from_date_dt, to_date_dt, config, organization_id
            ),
            'chart_active_users': self._get_chart_data(
                from_date_dt, to_date_dt, config, organization_id, 'active_users'
            )
        }

    def _get_date_ranges(self, from_date_dt, to_date_dt, config):
        """Generate date ranges based on config"""
        ranges = []
        
        if config == 'day':
            current = from_date_dt.date()
            while current <= to_date_dt.date():
                start = datetime.combine(current, datetime.min.time())
                end = datetime.combine(current, datetime.max.time())
                ranges.append({
                    'start': start,
                    'end': end,
                    'date': current.strftime("%Y-%m-%d"),
                    'formatted_date': current.strftime("%d/%m/%Y")
                })
                current += timedelta(days=1)
                
        elif config == 'week':
            current = from_date_dt.date()
            while current <= to_date_dt.date():
                week_start = current - timedelta(days=current.weekday())
                week_end = min(week_start + timedelta(days=6), to_date_dt.date())
                
                ranges.append({
                    'start': datetime.combine(week_start, datetime.min.time()),
                    'end': datetime.combine(week_end, datetime.max.time()),
                    'date': f"{week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}",
                    'formatted_date': f"{week_start.strftime('%d/%m')} - {week_end.strftime('%d/%m/%Y')}"
                })
                current = week_end + timedelta(days=1)
                
        elif config == 'month':
            current = from_date_dt.date().replace(day=1)
            while current <= to_date_dt.date():
                if current.month == 12:
                    next_month = current.replace(year=current.year + 1, month=1)
                else:
                    next_month = current.replace(month=current.month + 1)
                month_end = min(next_month - timedelta(days=1), to_date_dt.date())
                
                ranges.append({
                    'start': datetime.combine(current, datetime.min.time()),
                    'end': datetime.combine(month_end, datetime.max.time()),
                    'date': current.strftime("%Y-%m"),
                    'formatted_date': current.strftime("%m/%Y")
                })
                current = next_month
                
        elif config == 'year':
            for year in range(from_date_dt.year, to_date_dt.year + 1):
                year_start = max(datetime(year, 1, 1), from_date_dt)
                year_end = min(datetime(year, 12, 31, 23, 59, 59), to_date_dt)
                
                ranges.append({
                    'start': year_start,
                    'end': year_end,
                    'date': str(year),
                    'formatted_date': str(year)
                })
        
        return ranges

    def _get_chart_data(self, from_date_dt, to_date_dt, config, organization_id, data_type):
        """Generate chart data with optimized queries using bulk operations"""
        ranges = self._get_date_ranges(from_date_dt, to_date_dt, config)
        chart_data = []
        
        # Batch all queries for better performance
        for range_info in ranges:
            count = self._get_count_for_period(
                range_info['start'], range_info['end'], 
                organization_id, data_type
            )
            
            chart_data.append({
                'date': range_info['date'],
                'formatted_date': range_info['formatted_date'],
                'count': count,
            })
        
        return chart_data

    def _get_count_for_period(self, start_dt, end_dt, organization_id, data_type):
        """Get count for a specific period with optimized queries"""
        if data_type == 'users':
            query = User.objects.filter(created_time__gte=start_dt, created_time__lte=end_dt)
            if organization_id:
                query = query.filter(organization_memberships__organization_id=organization_id)
            return query.count()
            
        elif data_type == 'conversations':
            chat_count = self._get_chat_message_count(None, start_dt, end_dt, organization_id)
            request_count = self._get_request_log_count(None, start_dt, end_dt, organization_id)
            return chat_count + request_count
            
        elif data_type == 'active_users':
            query = RequestLog.objects.filter(timestamp__gte=start_dt, timestamp__lte=end_dt)
            if organization_id:
                query = query.filter(user__organization_memberships__organization_id=organization_id)
            return query.values('user_id').distinct().count()
        
        return 0

    def _get_detailed_chart_data(self, from_date_dt, to_date_dt, config, organization_id):
        """Generate detailed chart data with feature breakdown"""
        ranges = self._get_date_ranges(from_date_dt, to_date_dt, config)
        chart_data = []
        
        for range_info in ranges:
            # Get ChatMessage count
            chat_count = self._get_chat_message_count(None, range_info['start'], range_info['end'], organization_id)
            
            # Get RequestLog counts by feature
            feature_counts = {}
            base_query = RequestLog.objects.filter(
                timestamp__gte=range_info['start'], 
                timestamp__lte=range_info['end']
            )
            if organization_id:
                base_query = base_query.filter(user__organization_memberships__organization_id=organization_id)
            
            # Use single query with aggregation
            feature_data = base_query.filter(
                feature_name__in=ALLOWED_FEATURE_NAMES
            ).values('feature_name').annotate(count=Count('id'))
            
            for item in feature_data:
                feature_counts[item['feature_name']] = item['count']
            
            # Fill missing features with 0
            for feature in ALLOWED_FEATURE_NAMES:
                if feature not in feature_counts:
                    feature_counts[feature] = 0
            
            total_requests = sum(feature_counts.values())
            total_count = chat_count + total_requests
            
            chart_data.append({
                'date': range_info['date'],
                'formatted_date': range_info['formatted_date'],
                'chat_messages': chat_count,
                'feature_counts': feature_counts,
                'total_requests': total_requests,
                'count': total_count,
            })
        
        return chart_data


class ElasticLoaiVanBanStatsAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        # Cache key for Elasticsearch results
        cache_key = f"elastic_loai_van_ban_stats_{hash(str(request.data))}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return Response(cached_result, status=status.HTTP_200_OK)
            
        # Allowed document types
        ALLOWED_LOAI_VAN_BAN = {
            QUYET_DINH, THONG_TU, NGHI_QUYET, NGHI_DINH, THONG_TU_LIEN_TICH,
            LUAT, VAN_BAN_HOP_NHAT, PHAP_LENH, CONG_VAN, BO_LUAT,
            NGHI_QUYET_LIEN_TICH, CHI_THI, VAN_BAN_KHAC, LENH, HIEN_PHAP,
            VAN_BAN_LIEN_QUAN, THONG_BAO, CHUONG_TRINH, SAC_LENH, THONG_TU_LIEN_BO,
            HIEP_DINH, SAC_LUAT, BAO_CAO, CONG_DIEN, DIEU_UOC_QUOC_TE,
            HUONG_DAN, KE_HOACH, VAN_BAN_WTO, DU_THAO
        }

        es_client = init_es_client()
        if not es_client:
            return Response(
                {"error": "Could not initialize Elasticsearch client."}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        index_name = config('ELK_DOCUMENT_INDEX')
        
        # Default query if no body provided
        body = {
            "size": 0,
            "query": {
                "bool": {
                    "must": [{"term": {"type_of_van_ban": 1}}]
                }
            },
            "aggs": {
                "thong_ke_theo_loai_van_ban": {
                    "terms": {
                        "field": "loai_van_ban.keyword",
                        "size": 100
                    }
                }
            }
        }
        
        if request.data:
            body = request.data

        try:
            result = es_client.search(index=index_name, body=body)
            buckets = result.get('aggregations', {}).get('thong_ke_theo_loai_van_ban', {}).get('buckets', [])

            # Preferred order from frontend
            PREFERRED_ORDER = [
                "Hiến pháp", "Bộ luật", "Luật", "Pháp lệnh", "Nghị quyết", "Nghị quyết liên tịch",
                "Quyết định", "Nghị định", "Thông tư", "Thông tư liên tịch", "Công văn", "Báo cáo",
                "Chỉ thị", "Công điện", "Điều ước quốc tế", "Hướng dẫn", "Kế hoạch", "Thông báo",
                "Văn bản hợp nhất", "Văn bản khác", "Văn bản WTO", "Dự thảo", "Lệnh", "Sắc lệnh"
            ]

            # Filter and sort buckets
            filtered_buckets = [
                bucket for bucket in buckets
                if bucket.get('key') in PREFERRED_ORDER
            ]

            sorted_buckets = sorted(
                filtered_buckets,
                key=lambda x: PREFERRED_ORDER.index(x['key']) if x['key'] in PREFERRED_ORDER else len(PREFERRED_ORDER)
            )

            # Calculate total and prepend to results
            total_doc_count = sum(bucket.get('doc_count', 0) for bucket in filtered_buckets)
            final_buckets = [{"key": "Tất cả", "doc_count": total_doc_count}] + sorted_buckets
            
            # Cache for 10 minutes
            cache.set(cache_key, final_buckets, 600)
            
            return Response(final_buckets, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)