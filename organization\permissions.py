# permissions.py

from rest_framework.permissions import BasePermission
from .models import OrganizationMember
from .constants import *

class IsOrganizationAdminOrSuperAdmin(BasePermission):
    """
    Chỉ cho phép admin của tổ chức hoặc superuser thực hiện hành động.
    """

    def has_object_permission(self, request, view, obj):
        user = request.user

        if user.is_superuser:
            return True

        return OrganizationMember.objects.filter(
            organization=obj,
            user=user,
            role=ADMIN
        ).exists()
