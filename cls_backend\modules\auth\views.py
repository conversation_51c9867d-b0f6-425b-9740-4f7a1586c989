from django.contrib.auth import authenticate
from rest_framework import status
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import AllowAny, IsA<PERSON><PERSON>icated, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from django.utils.http import urlsafe_base64_decode
from django.utils.http import urlsafe_base64_encode
from cls_backend.models import Package, Quota, User, WorkSpace
from cls_backend.modules.auth.serializers import UserSerializer, ChangePasswordSerializer, UpdateUserProfileSerializer, AdminPasswordResetSerializer
from django.contrib.auth.tokens import default_token_generator
from rest_framework.exceptions import ValidationError
from django.utils.encoding import force_bytes, force_str
from django.core.mail import send_mail
from django.contrib.auth.hashers import check_password

from organization.constants import ADMIN, MEMBER
from organization.models import Organization, OrganizationMember
from .email_utils import send_password_change_notification, generate_random_password, send_account_creation_notification
import secrets
import string
from rest_framework_simplejwt.tokens import <PERSON><PERSON>resh<PERSON><PERSON>
from decouple import config
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from cls_backend.utils.file_utils import generate_presigned_url

from request_log.decorators import log_requests
class LoginAPIView(APIView):
    permission_classes = [AllowAny]
    serializer_class = UserSerializer

    
    @log_requests('login')
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')
        
        # Xác thực người dùng
        user = authenticate(username=email, password=password)
        if user is None:
            return Response({"error": "Email or password is incorrect"}, status=status.HTTP_404_NOT_FOUND)

        # Tạo refresh token và access token
        refresh = RefreshToken.for_user(user)
        
        # Kiểm tra và lấy workspace không thư mục (is_no_folder=True)
        no_folder_workspace = WorkSpace.objects.filter(user=user, is_no_folder=True, is_deleted=False).first()
        
        # Nếu không có workspace không thư mục, tạo mới
        if not no_folder_workspace:
            no_folder_workspace = WorkSpace.objects.create(
                user=user,
                name="Không phân loại",
                description="Workspace mặc định không phân loại",
                is_no_folder=True
            )
        
        # Trả về thông tin user cùng token và workspace_id
        return Response({
            "user": self.serializer_class(user).data,
            "workspace_id": str(no_folder_workspace.id),
            "refresh": str(refresh),
            "access": str(refresh.access_token)
        }, status=status.HTTP_200_OK)


class RetriveUserAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserSerializer

    def get(self, request):
        user = request.user
        user_data = self.serializer_class(user).data
        return Response(user_data)


# class CreateUserAPIView(APIView):
#     serializer_class = UserSerializer
#     permission_classes = [IsAdminUser]

#     def post(self, request):
#         user_serializer = self.serializer_class(data=request.data)
#         user_serializer.is_valid(raise_exception=True)
#         user_serializer.save()
#         return Response(user_serializer.data)
    
class CreateUserAPIView(APIView):
    serializer_class = UserSerializer
    permission_classes = [IsAdminUser]

    def post(self, request):
        # Kiểm tra email đã tồn tại chưa
        if User.objects.filter(email=request.data.get("email")).exists():
            return Response({"error": "Email đã tồn tại."}, status=status.HTTP_400_BAD_REQUEST)

        # Tự động generate mật khẩu nếu không được cung cấp
        request_data = request.data.copy()
        generated_password = None

        if not request_data.get('password'):
            generated_password = generate_random_password(length=8)
            request_data['password'] = generated_password
        else:
            generated_password = request_data['password']

        # Lấy thông tin organization và role từ request
        organization_id = request.data.get('organization')
        role = request.data.get('role')

        user_serializer = self.serializer_class(data=request_data)
        user_serializer.is_valid(raise_exception=True)
        user = user_serializer.save()

        # Nếu role là ADMIN thì set is_staff=True cho user
        if str(role).upper() == ADMIN:
            user.is_staff = True
            user.save(update_fields=["is_staff"])

        # Lấy Package mặc định
        default_package = Package.objects.first()
        if not default_package:
            user.delete()  # Xóa User nếu không tìm thấy Package
            return Response({"error": "Không tìm thấy gói mặc định."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Tạo Quota cho User mới
        try:
            Quota.objects.create(user=user, package=default_package)
        except Exception as e:
            user.delete()  # Xóa User nếu không tạo được Quota
            return Response({"error": f"Lỗi khi tạo Quota: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Tạo OrganizationMember nếu có organization và role
        organization_member_created = False
        if organization_id and role:
            try:
                # Kiểm tra Organization có tồn tại không
                organization = Organization.objects.get(id=organization_id)

                # Validate role
                if role not in [ADMIN, MEMBER]:
                    user.delete()  # Xóa User nếu role không hợp lệ
                    return Response({"error": f"Role phải là '{ADMIN}' hoặc '{MEMBER}'"}, status=status.HTTP_400_BAD_REQUEST)

                # Tạo OrganizationMember
                OrganizationMember.objects.create(
                    user=user,
                    organization=organization,
                    role=role,
                    is_active=True
                )
                organization_member_created = True

            except Organization.DoesNotExist:
                user.delete()  # Xóa User nếu Organization không tồn tại
                return Response({"error": f"Organization với id {organization_id} không tồn tại."}, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                user.delete()  # Xóa User nếu không tạo được OrganizationMember
                return Response({"error": f"Lỗi khi tạo OrganizationMember: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Lấy frontend URL từ request hoặc sử dụng mặc định
        frontend_url = request.META.get("HTTP_REFERER") or request.META.get("HTTP_ORIGIN") or "https://cls.cmcati.vn"

        # Gửi email thông báo tạo tài khoản thành công
        email_sent = send_account_creation_notification(
            user=user,
            password=generated_password,
            frontend_url=frontend_url
        )

        # Chuẩn bị response data
        response_data = user_serializer.data.copy()
        response_data.update({
            'password_generated': generated_password,  # Trả về mật khẩu đã tạo
            'email_sent': email_sent,
            'organization_member_created': organization_member_created,
            'message': 'Tài khoản đã được tạo thành công và email thông báo đã được gửi.'
        })

        return Response(response_data, status=status.HTTP_201_CREATED)



# API cập nhật thông tin tài khoản người dùng
class UpdateUserProfileView(APIView):
    """
    API để cập nhật thông tin profile người dùng

    Các trường có thể cập nhật:
    - fullname: Họ tên người dùng
    - avatar: Ảnh đại diện
    - password: Mật khẩu mới (yêu cầu current_password)

    Method: PUT
    Authentication: Required (JWT Token)
    """
    permission_classes = [IsAuthenticated]
    serializer_class = UpdateUserProfileSerializer

    def put(self, request):
        """
        Cập nhật thông tin profile người dùng
        """
        user = request.user
        serializer = self.serializer_class(user, data=request.data, partial=True)
        if serializer.is_valid():
            updated_user = serializer.save()
            return Response({
                "success": True,
                "message": "Cập nhật thông tin thành công",
                "data": serializer.data
            }, status=status.HTTP_200_OK)
        return Response({
            "success": False,
            "message": "Dữ liệu không hợp lệ",
            "errors": serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        """
        Lấy thông tin profile hiện tại của người dùng
        """
        try:
            user = request.user
            serializer = self.serializer_class(user)

            return Response({
                "success": True,
                "message": "Lấy thông tin thành công",
                "data": serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                "success": False,
                "message": "Có lỗi xảy ra khi lấy thông tin",
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PasswordChangeView(GenericViewSet, UpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ChangePasswordSerializer

    def get_object(self, *args, **kwargs):
        obj = self.request.user
        return obj

    def update(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            # Check current password
            if not user.check_password(serializer.data.get("current_password")):
                return Response({"error": "Invalid current password."}, status=status.HTTP_400_BAD_REQUEST)
            # set_password also hashes the password that the user will get
            user.set_password(serializer.data.get("new_password"))
            user.save()

            # Gửi email thông báo đổi mật khẩu thành công
            email_sent = send_password_change_notification(
                user=user,
                is_reset_by_admin=False
            )

            response = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': 'Password updated successfully',
                'email_sent': email_sent
            }
            return Response(response)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetRequestView(APIView): 
    permission_classes = [AllowAny]

    # def post(self, request):
    #     email = request.data.get("email")

    #     try:
    #         user = User.objects.get(email=email)
    #     except User.DoesNotExist:
    #         return Response({"error": "Email không tồn tại trong hệ thống."}, status=status.HTTP_400_BAD_REQUEST)

    #     # Tạo token
    #     uid = urlsafe_base64_encode(force_bytes(user.pk))
    #     token = default_token_generator.make_token(user)

    #     # Lấy frontend URL từ Referer / Origin
    #     frontend_url = request.META.get("HTTP_REFERER") or request.META.get("HTTP_ORIGIN")
    #     if not frontend_url:
    #         frontend_url = "https://clsqh.cmcati.vn"  # fallback mặc định nếu không có header

    #     reset_url = f"{frontend_url.rstrip('/')}/cls/pages/authentication/reset-password/{uid}/{token}/"

    #     send_mail(
    #         "Yêu cầu đặt lại mật khẩu",
    #         f"Nhấn vào link để đặt lại mật khẩu: {reset_url}",
    #         "<EMAIL>",
    #         [email]
    #     )

    #     return Response({"message": "Email đặt lại mật khẩu đã được gửi."}, status=status.HTTP_200_OK)

    def post(self, request):
            email = request.data.get("email")

            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                return Response({"error": "Email không tồn tại trong hệ thống."}, status=status.HTTP_400_BAD_REQUEST)

            # Tạo token
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)

            # URL frontend
            frontend_url = request.META.get("HTTP_REFERER") or request.META.get("HTTP_ORIGIN") or "https://clsqh.cmcati.vn"
            reset_url = f"{frontend_url.rstrip('/')}/cls/pages/authentication/reset-password/{uid}/{token}/"

            # Gửi email đẹp hơn
            subject = "🔐 Yêu cầu đặt lại mật khẩu"
            from_email = "<EMAIL>"
            to_email = [email]

            text_content = f"Nhấn vào link sau để đặt lại mật khẩu của bạn: {reset_url}"

            html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px;">
                    <div style="
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #ffffff;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                    padding: 30px;
                    border: 1px solid #e0e0e0;
                    ">
                    <h2 style="color: #333333;">Yêu cầu đặt lại mật khẩu</h2>
                    <p>Xin chào <strong>{user.fullname or user.email}</strong>,</p>
                    <p>Chúng tôi đã nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.</p>
                    <p>Nhấn vào nút bên dưới để đặt lại mật khẩu:</p>
                    <p><strong>Lưu ý:</strong> Liên kết sẽ hết hạn sau <strong>8 tiếng</strong> kể từ khi nhận được email này.</p>

                    <p style="text-align: center; margin: 30px 0;">
                        <a href="{reset_url}" style="
                        background-color: #007bff;
                        color: white;
                        padding: 12px 24px;
                        text-decoration: none;
                        border-radius: 5px;
                        display: inline-block;
                        font-weight: bold;
                        ">
                        Đặt lại mật khẩu
                        </a>
                    </p>

                    <p>Nếu bạn không yêu cầu điều này, bạn có thể bỏ qua email này.</p>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eeeeee;" />
                    <p style="font-size: 12px; color: gray; text-align: center;">
                        Email được gửi tự động từ hệ thống CLS. Vui lòng không trả lời email này.
                    </p>
                    </div>
                </body>
            </html>
            """

            msg = EmailMultiAlternatives(subject, text_content, from_email, to_email)
            msg.attach_alternative(html_content, "text/html")
            msg.send()

            return Response({"message": "Email đặt lại mật khẩu đã được gửi."}, status=status.HTTP_200_OK)
class PasswordResetValidateTokenView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (User.DoesNotExist, ValueError, TypeError, OverflowError):
            return Response({"error": "Liên kết không hợp lệ."}, status=status.HTTP_400_BAD_REQUEST)

        if not default_token_generator.check_token(user, token):
            return Response({"error": "Liên kết đã hết hạn hoặc không hợp lệ."}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Liên kết hợp lệ."}, status=status.HTTP_200_OK)  
class PasswordResetConfirmView(APIView):
    permission_classes = [AllowAny]
    def post(self, request, uidb64, token):
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            user = User.objects.get(pk=uid)
        except (User.DoesNotExist, ValueError):
            return Response({"error": "Link không hợp lệ."}, status=status.HTTP_400_BAD_REQUEST)
        if not default_token_generator.check_token(user, token):
            return Response({"error": "Token không hợp lệ hoặc đã hết hạn."}, status=status.HTTP_400_BAD_REQUEST)
        new_password = request.data.get("new_password")
        if not new_password or len(new_password) < 6:
            return Response({"error": "Mật khẩu mới phải có ít nhất 6 ký tự."}, status=status.HTTP_400_BAD_REQUEST)
        if check_password(new_password, user.password):
            return Response({"error": "Mật khẩu mới không được giống với mật khẩu hiện tại."}, status=status.HTTP_400_BAD_REQUEST)
        user.set_password(new_password)
        user.save()

        return Response({"message": "Mật khẩu đã được đặt lại thành công."}, status=status.HTTP_200_OK)


class RefreshAvatarAPIView(APIView):
    """
    API để lấy lại URL avatar mới khi URL cũ hết hạn

    Method: GET
    Authentication: Required (JWT Token)
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Lấy lại URL avatar mới với thời hạn mới

        Response example:
        {
            "success": true,
            "message": "Lấy avatar thành công",
            "data": {
                "avatar_url": "https://s3.example.com/avatars/user123.jpg?signature=..."
            }
        }
        """
        try:
            user = request.user

            # Kiểm tra user có avatar không
            if not user.avatar:
                return Response({
                    "success": False,
                    "message": "Người dùng chưa có avatar",
                    "data": {
                        "avatar_url": None
                    }
                }, status=status.HTTP_200_OK)

            # Tạo presigned URL mới cho avatar (mặc định hết hạn sau 1 giờ)
            avatar_url = generate_presigned_url(user.avatar.name, expiration=3600)

            if not avatar_url:
                return Response({
                    "success": False,
                    "message": "Không thể tạo URL avatar",
                    "data": {
                        "avatar_url": None
                    }
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({
                "success": True,
                "message": "Lấy avatar thành công",
                "data": {
                    "avatar_url": avatar_url
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                "success": False,
                "message": f"Lỗi khi lấy avatar: {str(e)}",
                "data": {
                    "avatar_url": None
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminPasswordResetView(APIView):
    """
    API cho phép super user reset mật khẩu của user khác
    Mật khẩu mới sẽ được tự động generate và gửi qua email

    Method: POST
    Authentication: Required (JWT Token)
    Permission: Super User only

    Body:
    {
        "user_id": 123
    }
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AdminPasswordResetSerializer
    def post(self, request):
        # Kiểm tra quyền super user
        if not request.user.is_superuser:
            return Response({
                "error": "Chỉ super user mới có quyền reset mật khẩu của user khác."
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        user_id = serializer.validated_data['user_id']

        try:
            # Lấy user cần reset
            target_user = User.objects.get(id=user_id)

            # Kiểm tra không được reset super user khác
            if target_user.is_superuser:
                return Response({
                    "error": "Không thể reset mật khẩu của super user khác."
                }, status=status.HTTP_403_FORBIDDEN)

            # Tự động generate mật khẩu mới
            new_password = generate_random_password(length=10)

            # Reset mật khẩu
            target_user.set_password(new_password)
            target_user.save()

            # Gửi email thông báo với mật khẩu mới
            email_sent = send_password_change_notification(
                user=target_user,
                new_password=new_password,
                is_reset_by_admin=True
            )

            response_data = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': f'Đã reset mật khẩu thành công cho user {target_user.email}',
                'data': {
                    'user_email': target_user.email,
                    'user_fullname': target_user.fullname,
                    'new_password': new_password,  # Trả về mật khẩu mới trong response
                    'email_sent': email_sent
                }
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({
                "error": "User không tồn tại."
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "error": f"Có lỗi xảy ra: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
